"use client";

import * as React from "react";
import { Check, ChevronsUpDown, PlusCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  PREDEFINED_VARIANT_TYPES,
  getAllVariantTypes,
  getVariantTypeByName
} from "@/lib/constants/predefinedVariants";

export interface VariantTypeOption {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_predefined: boolean;
}

interface VariantTypeSelectorProps {
  selectedTypes: string[]; // Array of selected variant type names
  onChange: (_selectedTypes: string[]) => void;
  onCreateCustomType?: (_typeName: string, _displayName: string) => Promise<VariantTypeOption | null>;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyPlaceholder?: string;
  createPlaceholder?: string;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  maxSelections?: number;
}

export default function VariantTypeSelector({
  selectedTypes,
  onChange,
  onCreateCustomType,
  placeholder = "Select variant types...",
  searchPlaceholder = "Search variant types...",
  emptyPlaceholder = "No variant types found.",
  createPlaceholder = 'Create "{value}" type',
  className,
  isLoading = false,
  disabled = false,
  maxSelections = 5,
}: VariantTypeSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState("");

  // Convert predefined types to options
  const predefinedOptions: VariantTypeOption[] = PREDEFINED_VARIANT_TYPES.map((type, index) => ({
    id: type.id || `predefined-${index}`,
    name: type.name,
    display_name: type.display_name,
    description: type.description || undefined,
    is_predefined: true,
  }));

  // For now, we'll only use predefined options
  // In the future, this could be extended to include custom types from the database
  const allOptions = predefinedOptions;

  const handleSelect = (typeName: string) => {
    const newSelected = selectedTypes.includes(typeName)
      ? selectedTypes.filter((name) => name !== typeName)
      : [...selectedTypes, typeName];
    
    // Respect max selections limit
    if (newSelected.length <= maxSelections) {
      onChange(newSelected);
    }
  };

  const handleRemove = (typeName: string) => {
    onChange(selectedTypes.filter((name) => name !== typeName));
  };

  // Helper function to convert display name to internal name
  const displayNameToName = (displayName: string): string => {
    return displayName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '');
  };

  const handleCreate = async () => {
    if (!onCreateCustomType || !searchTerm.trim()) return;

    const displayName = searchTerm.trim();
    const name = displayNameToName(displayName);

    // Check if it already exists
    if (allOptions.some(option => option.name === name)) {
      return;
    }

    try {
      const newType = await onCreateCustomType(name, displayName);
      if (newType) {
        // Add the new type to selected types
        if (selectedTypes.length < maxSelections) {
          onChange([...selectedTypes, newType.name]);
        }
        setSearchTerm("");
        setOpen(false);
      }
    } catch (error) {
      console.error('Error creating custom variant type:', error);
    }
  };

  // Filter options based on search term
  const filteredOptions = allOptions.filter((option) =>
    option.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (option.description && option.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Get selected option details for display
  const selectedOptions = allOptions.filter((option) =>
    selectedTypes.includes(option.name)
  );

  const showCreateOption =
    onCreateCustomType &&
    searchTerm.trim() &&
    !allOptions.some(
      (option) => option.name.toLowerCase() === displayNameToName(searchTerm).toLowerCase()
    ) &&
    selectedTypes.length < maxSelections;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild disabled={disabled || isLoading}>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between h-auto min-h-[2.5rem] bg-white dark:bg-neutral-800 border-neutral-300 dark:border-neutral-600 text-neutral-900 dark:text-white",
            selectedOptions.length === 0 && "text-muted-foreground",
            className
          )}
        >
          <div className="flex flex-wrap gap-1 flex-1">
            {selectedOptions.length === 0 ? (
              <span>{placeholder}</span>
            ) : (
              selectedOptions.map((option) => (
                <Badge
                  key={option.name}
                  variant="secondary"
                  className="text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove(option.name);
                  }}
                >
                  {option.display_name}
                  <button
                    className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleRemove(option.name);
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleRemove(option.name);
                    }}
                  >
                    ×
                  </button>
                </Badge>
              ))
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchTerm}
            onValueChange={setSearchTerm}
            disabled={isLoading}
          />
          <CommandList>
            <ScrollArea className="max-h-[200px]">
              <CommandEmpty>
                {showCreateOption ? " " : emptyPlaceholder}
              </CommandEmpty>
              
              {/* Predefined Types */}
              <CommandGroup heading="Predefined Types">
                {filteredOptions
                  .filter(option => option.is_predefined)
                  .map((option) => (
                    <CommandItem
                      key={option.name}
                      value={option.display_name}
                      onSelect={() => {
                        handleSelect(option.name);
                        setOpen(true); // Keep popover open after selection
                      }}
                      disabled={selectedTypes.length >= maxSelections && !selectedTypes.includes(option.name)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedTypes.includes(option.name)
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col">
                        <span className="font-medium">{option.display_name}</span>
                        {option.description && (
                          <span className="text-xs text-muted-foreground">
                            {option.description}
                          </span>
                        )}
                      </div>
                    </CommandItem>
                  ))}
              </CommandGroup>

              {/* Custom Types (if any) */}
              {filteredOptions.some(option => !option.is_predefined) && (
                <CommandGroup heading="Custom Types">
                  {filteredOptions
                    .filter(option => !option.is_predefined)
                    .map((option) => (
                      <CommandItem
                        key={option.name}
                        value={option.display_name}
                        onSelect={() => {
                          handleSelect(option.name);
                          setOpen(true);
                        }}
                        disabled={selectedTypes.length >= maxSelections && !selectedTypes.includes(option.name)}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            selectedTypes.includes(option.name)
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        <div className="flex flex-col">
                          <span className="font-medium">{option.display_name}</span>
                          <span className="text-xs text-muted-foreground">Custom type</span>
                        </div>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}

              {/* Create Custom Type Option */}
              {showCreateOption && (
                <>
                  <CommandSeparator />
                  <CommandGroup>
                    <CommandItem
                      value={searchTerm}
                      onSelect={handleCreate}
                      className="text-muted-foreground cursor-pointer"
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      {createPlaceholder.replace("{value}", searchTerm)}
                    </CommandItem>
                  </CommandGroup>
                </>
              )}
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

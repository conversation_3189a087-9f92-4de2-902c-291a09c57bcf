import { notFound } from "next/navigation";
import { Metadata } from "next";
import { getSecureBusinessProfileBySlug } from "@/lib/actions/businessProfiles";
import {
  getProductBySlug,
  getProductWithVariantsBySlug,
  getBusinessWithContactInfo,
  getMoreProductsFromBusiness,
  getProductsFromOtherBusinesses
} from "../actions";
import ProductDetailClient from "./ProductDetailClient";
import { createClient } from "@/utils/supabase/server";
import { AdData } from "@/types/ad";
import OfflineProductMessage from "../components/OfflineProductMessage";

// Define a type for the profile data we need for these helper functions
type ProfilePlanData = {
  subscription_status: string | null;
  plan_id: string | null;
};

// Helper function to determine user plan
const getUserPlan = (
  profile: ProfilePlanData
): "free" | "basic" | "growth" | "pro" | "enterprise" | undefined => {
  // Simply return the plan_id from the subscription data
  switch (profile.plan_id) {
    case "free":
      return "free";
    case "growth":
      return "growth";
    case "pro":
      return "pro";
    case "enterprise":
      return "enterprise";
    case "basic":
      return "basic";
    default:
      return "free"; // Default to free if no plan_id specified
  }
};

// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = (): boolean => {
  // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
  return true; // Always show platform ads as fallback
};

export default async function ProductPage({
  params,
}: {
  params: Promise<{ cardSlug: string; productSlug: string }>;
}) {
  const { cardSlug, productSlug } = await params;

  // Fetch the business profile
  const { data: businessProfile, error: profileError } =
    await getSecureBusinessProfileBySlug(cardSlug);

  if (profileError || !businessProfile) {
    console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
    notFound();
  }

  // Check if the profile is online
  if (businessProfile.status !== "online") {
    console.log(
      `Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`
    );
    // Show offline message instead of 404
    return <OfflineProductMessage />;
  }

  // Fetch the product with variants by slug
  const { data: productWithVariants } = await getProductWithVariantsBySlug(
    productSlug,
    businessProfile.id
  );

  if (!productWithVariants) {
    // For non-existent products, just use notFound() without logging an error
    // This is an expected case, not an error condition
    notFound();
  }

  // Fetch business details with contact information
  const { data: businessDetails } = await getBusinessWithContactInfo(
    businessProfile.id
  );

  // Fetch more products from the same business
  const { data: moreBusinessProducts } = await getMoreProductsFromBusiness(
    businessProfile.id,
    productWithVariants.id as string
  );

  // Fetch products from other businesses
  const { data: otherBusinessProducts } = await getProductsFromOtherBusinesses(
    businessProfile.id
  );

  // Ensure arrays are properly initialized to prevent "Cannot read properties of undefined (reading 'length')" error
  const safeBusinessProducts = moreBusinessProducts || [];
  const safeOtherBusinessProducts = otherBusinessProducts || [];

  // Determine user plan
  const _userPlan = getUserPlan(businessProfile);
  let topAdData: AdData = null;

  // Fetch platform ads for all businesses (Pro/Enterprise can override with their own custom ads)
  if (shouldShowPlatformAds()) {
    try {
      const supabase = await createClient();

      // First, check if the custom_ad_targets table exists (for backward compatibility)
      const { count, error: tableCheckError } = await supabase
        .from("custom_ad_targets")
        .select("*", { count: "exact", head: true });

      // If the table exists and migration has been applied
      if (count !== null && !tableCheckError) {
        // Use the get_ad_for_pincode function to find the appropriate ad
        const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
        const { data: adData, error: adError } = await supabase.rpc(
          "get_ad_for_pincode",
          { target_pincode: pincode }
        );

        if (adData && adData.length > 0) {
          // Found an ad (either pincode-specific or global)
          topAdData = {
            type: "custom",
            imageUrl: adData[0].ad_image_url,
            linkUrl: adData[0].ad_link_url,
          };
        } else {
          // No custom ads found or error occurred
          if (adError)
            console.error(`Error fetching ad for pincode ${pincode}:`, adError);
          topAdData = null; // Show placeholder when no custom ads are available
        }
      } else {
        // Fallback to old approach if migration hasn't been applied yet
        if (businessProfile.pincode) {
          const { data: customAd } = await supabase
            .from("custom_ads")
            .select("ad_image_url, ad_link_url")
            .eq("is_active", true)
            .or(
              `targeting_locations.eq.'"global"',targeting_locations.cs.'["${businessProfile.pincode}"]'`
            )
            .order("created_at", { ascending: false })
            .limit(1)
            .maybeSingle();

          if (customAd) {
            topAdData = {
              type: "custom",
              imageUrl: customAd.ad_image_url,
              linkUrl: customAd.ad_link_url,
            };
          } else {
            // No matching custom ad found
            topAdData = null;
          }
        } else {
          // If business has no pincode, try to find global ads
          const { data: globalAd } = await supabase
            .from("custom_ads")
            .select("ad_image_url, ad_link_url")
            .eq("is_active", true)
            .eq("targeting_locations", '"global"')
            .order("created_at", { ascending: false })
            .limit(1)
            .maybeSingle();

          if (globalAd) {
            topAdData = {
              type: "custom",
              imageUrl: globalAd.ad_image_url,
              linkUrl: globalAd.ad_link_url,
            };
          } else {
            topAdData = null;
          }
        }
      }
    } catch (adFetchError) {
      console.error(`Error fetching custom ad:`, adFetchError);
      topAdData = null; // fallback on error
    }
  }

  return (
    <ProductDetailClient
      product={productWithVariants}
      variants={productWithVariants.variants || []}
      businessSlug={cardSlug}
      businessName={businessDetails?.business_name || businessProfile.business_name || ""}
      whatsappNumber={businessDetails?.whatsapp_number || null}
      phoneNumber={businessDetails?.phone || null}
      businessProducts={safeBusinessProducts}
      otherBusinessProducts={safeOtherBusinessProducts}
      topAdData={topAdData}
      businessCustomAd={businessProfile.custom_ads}
      userPlan={_userPlan}
    />
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ cardSlug: string; productSlug: string }>;
}): Promise<Metadata> {
  const { cardSlug, productSlug } = await params;
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/${cardSlug}/product/${productSlug}`;

  // Fetch the business profile
  const { data: businessProfile, error: profileError } = await getSecureBusinessProfileBySlug(
    cardSlug
  );

  if (profileError || !businessProfile) {
    // Use notFound() to trigger the 404 page for non-existent business slugs
    notFound();
  }

  // Fetch the product with variants for better SEO
  const { data: productWithVariants } = await getProductWithVariantsBySlug(
    productSlug,
    businessProfile.id
  );

  // Use the product with variants data
  const product = productWithVariants;

  // Fetch business details with contact information
  const { data: businessDetails } = await getBusinessWithContactInfo(
    businessProfile.id
  );

  if (!product) {
    // Use notFound() to trigger the 404 page for non-existent product slugs
    // This is an expected case, not an error condition
    notFound();
  }

  const businessName = businessDetails?.business_name || businessProfile.business_name || "Business";
  const productName = product.name || "Product";

  // Enhanced description with variant information
  let productDescription = product.description || `${productName} by ${businessName}`;
  if (product && product.variants && product.variants.length > 0) {
    const variantTypes = new Set<string>();
    product.variants.forEach(variant => {
      Object.keys(variant.variant_values).forEach(type => variantTypes.add(type));
    });

    if (variantTypes.size > 0) {
      const variantTypesText = Array.from(variantTypes).join(', ');
      productDescription += ` Available in multiple ${variantTypesText} options.`;
    }
  }

  return {
    title: `${productName} | ${businessName}`,
    description: productDescription.substring(0, 160),
    openGraph: {
      title: `${productName} | ${businessName}`,
      description: productDescription.substring(0, 160),
      url: pageUrl,
      images: product.image_url
        ? [
            {
              url: product.image_url,
              width: 1200,
              height: 630,
              alt: productName,
            },
          ]
        : undefined,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${productName} | ${businessName}`,
      description: productDescription.substring(0, 160),
      images: product.image_url ? [product.image_url] : undefined,
    },
  };
}

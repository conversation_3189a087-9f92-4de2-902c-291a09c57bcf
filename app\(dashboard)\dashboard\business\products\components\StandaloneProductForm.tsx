"use client";

// No React hooks needed at the top level
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { motion } from "framer-motion";
import { Loader2, Package, ShoppingBag } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProductServiceData } from "../actions";
import { useProductMultiImageUpload } from "./hooks/useProductMultiImageUpload";
import ProductImageCropDialog from "./ProductImageCropDialog";
import ProductMultiImageUpload from "./ProductMultiImageUpload";
import EnhancedGlowButton from "@/app/(dashboard)/dashboard/business/plan/components/EnhancedGlowButton";
import VariantTable from "./VariantTable";
import VariantForm from "./VariantForm";
import { ProductVariant } from "@/types/variants";
import { Plus, Settings, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

// Zod schema
const formSchema = z
  .object({
    product_type: z.enum(['physical', 'service'], {
      required_error: 'Please select a product type.',
    }).default('physical'),
    name: z
      .string()
      .min(1, { message: "Product/Service name is required." })
      .max(100, { message: "Name cannot exceed 100 characters." }),
    description: z
      .string()
      .max(500, { message: "Description cannot exceed 500 characters." })
      .optional()
      .or(z.literal("")),
    base_price: z.coerce
      .number({ required_error: "Base price is required.", invalid_type_error: "Base price must be a number." })
      .positive({ message: "Base price must be positive." }),
    discounted_price: z.coerce
      .number({ invalid_type_error: "Discounted price must be a number." })
      .positive({ message: "Discounted price must be positive." })
      .optional()
      .nullable(),
    is_available: z.boolean().default(true),
  })
  .refine(
    (data) => !data.base_price || !data.discounted_price || data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

export type ProductFormValues = z.infer<typeof formSchema>;

interface StandaloneProductFormProps {
  initialData?: ProductServiceData | null;
  initialVariants?: ProductVariant[];
  onSubmit: (
    _values: ProductFormValues & { variants?: ProductVariant[] },
    _imageFiles?: (File | null)[],
    _featuredImageIndex?: number,
    _removedImageIndices?: number[]
  ) => Promise<void>;
  isSubmitting: boolean;
  isEditing: boolean;
  planLimit?: number;
  currentAvailableCount?: number;
}

export default function StandaloneProductForm({
  initialData,
  initialVariants,
  onSubmit,
  isSubmitting,
  isEditing,
  planLimit,
  currentAvailableCount,
}: StandaloneProductFormProps) {
  // Variant management state
  const [variants, setVariants] = useState<ProductVariant[]>(initialVariants || []);
  const [showVariantForm, setShowVariantForm] = useState(false);
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null);
  const [showVariantManagement, setShowVariantManagement] = useState(false);
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24
      }
    }
  };

  // Use our custom hook for multi-image upload with cropping
  const {
    images,
    featuredImageIndex,
    imageToCrop,
    imageErrorDisplay,
    addImageSlot,
    removeImage,
    setAsFeatured,
    handleFileSelect,
    handleCropComplete,
    handleCropDialogClose
  } = useProductMultiImageUpload({
    initialImageUrls: initialData?.images || (initialData?.image_url ? [initialData.image_url] : null),
    initialFeaturedIndex: initialData?.featured_image_index || 0,
    maxImages: 5
  });

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      product_type: initialData?.product_type ?? 'physical',
      name: initialData?.name ?? "",
      description: initialData?.description ?? "",
      base_price: initialData?.base_price !== undefined ? initialData.base_price : undefined,
      discounted_price: initialData?.discounted_price !== undefined ? initialData.discounted_price : null,
      is_available: initialData?.is_available ?? true,
    },
  });

  // Variant management functions
  const handleAddVariant = () => {
    setEditingVariant(null);
    setShowVariantForm(true);
  };

  const handleEditVariant = (variant: ProductVariant) => {
    setEditingVariant(variant);
    setShowVariantForm(true);
  };

  const handleDeleteVariant = (variantId: string) => {
    setVariants(prev => prev.filter(v => v.id !== variantId));
    toast.success("Variant deleted successfully");
  };

  const handleToggleVariantAvailability = (variantId: string, isAvailable: boolean) => {
    setVariants(prev => prev.map(v =>
      v.id === variantId ? { ...v, is_available: isAvailable } : v
    ));
    toast.success(`Variant ${isAvailable ? 'enabled' : 'disabled'} successfully`);
  };

  const handleCancelVariantForm = () => {
    setShowVariantForm(false);
    setEditingVariant(null);
  };

  const handleVariantSubmit = async (
    variantData: any,
    imageFiles?: (File | null)[],
    featuredImageIndex?: number,
    _removedImageIndices?: number[]
  ) => {
    try {
      // Store image files in browser state for later upload when product is saved
      const variantImages = imageFiles?.filter(file => file !== null) as File[] || [];

      // Create preview URLs for new images, but preserve existing URLs
      const imageUrls = variantImages.length > 0
        ? variantImages.map(file => URL.createObjectURL(file))
        : (editingVariant?.images || []);

      if (editingVariant) {
        // Update existing variant
        setVariants(prev => prev.map(v =>
          v.id === editingVariant.id
            ? {
                ...v,
                variant_name: variantData.variant_name,
                variant_values: variantData.variant_values,
                base_price: variantData.base_price,
                discounted_price: variantData.discounted_price,
                is_available: variantData.is_available,
                images: imageUrls, // Use existing URLs or new preview URLs
                featured_image_index: featuredImageIndex || 0,
                updated_at: new Date().toISOString(),
                // Store actual files for upload (only if new images were added)
                _imageFiles: variantImages.length > 0 ? variantImages : v._imageFiles,
              }
            : v
        ));
        toast.success("Variant updated successfully");
      } else {
        // Add new variant
        const newVariant: ProductVariant = {
          id: `temp-${Date.now()}`,
          product_id: initialData?.id || '',
          variant_name: variantData.variant_name,
          variant_values: variantData.variant_values,
          base_price: variantData.base_price,
          discounted_price: variantData.discounted_price,
          is_available: variantData.is_available,
          images: imageUrls, // Store preview URLs for display
          featured_image_index: featuredImageIndex || 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          // Store actual files for upload
          _imageFiles: variantImages,
        };
        setVariants(prev => [...prev, newVariant]);
        toast.success("Variant added successfully");
      }
      setShowVariantForm(false);
      setEditingVariant(null);
    } catch (error) {
      console.error("Error saving variant:", error);
      toast.error("Failed to save variant");
    }
  };



  // Loading effect removed

  const processSubmit = (values: ProductFormValues) => {
    // Collect all image files
    const imageFiles = images.map(img => img.file);

    // Get indices of removed images by tracking which original indices are missing
    const removedImageIndices: number[] = [];
    const initialCount = initialData?.images?.length || (initialData?.image_url ? 1 : 0);

    if (initialCount > 0) {
      // Create a set of original indices that are still present
      const presentOriginalIndices = new Set(
        images
          .filter(img => img.originalIndex !== undefined)
          .map(img => img.originalIndex)
      );

      // Find which original indices are missing
      for (let i = 0; i < initialCount; i++) {
        if (!presentOriginalIndices.has(i)) {
          removedImageIndices.push(i);
        }
      }
    }

    console.log('Initial image count:', initialCount);
    console.log('Current images:', images);
    console.log('Removed image indices:', removedImageIndices);
    console.log('Variants to be created:', variants);

    // Pass variants for both new and existing products
    // The parent component will handle creating/updating variants appropriately
    const extendedValues = {
      ...values,
      variants: variants, // Include variants for both new and existing products
    };

    onSubmit(
      extendedValues,
      imageFiles,
      featuredImageIndex,
      removedImageIndices
    );
  };

  return (
    <div className="w-full">
      <motion.div
        className="bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm overflow-hidden"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header */}
        <div className="relative h-16 bg-gradient-to-r from-neutral-50 to-white dark:from-neutral-900 dark:to-black border-b border-neutral-200 dark:border-neutral-800 flex items-center px-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              {isEditing ? (
                <Package className="h-5 w-5 text-[var(--brand-gold)]" />
              ) : (
                <ShoppingBag className="h-5 w-5 text-[var(--brand-gold)]" />
              )}
            </div>
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
              {isEditing ? "Edit Product Details" : "New Product Details"}
            </h3>
          </div>
        </div>

        <div className="p-6">
          <motion.p variants={itemVariants} className="text-sm text-neutral-500 dark:text-neutral-400 mb-6">
            {isEditing
              ? "Update the details for this item. All fields marked with * are required."
              : "Fill in the details for the new product or service. All fields marked with * are required."}
          </motion.p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(processSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div variants={itemVariants} className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="product_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product Type *</FormLabel>
                        <Select
                          disabled={isSubmitting}
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select product type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="physical">Physical Product</SelectItem>
                            <SelectItem value="service">Service</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose whether this is a physical product or a service.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={itemVariants} className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter product/service name"
                            {...field}
                            disabled={isSubmitting}
                            className="h-10"
                          />
                        </FormControl>
                        <FormDescription>
                          The name of your product or service (max 100 characters).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={itemVariants} className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter product/service description"
                            className="resize-none min-h-[120px]"
                            {...field}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <FormDescription>
                          Describe your product or service (max 500 characters).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={itemVariants}>
                  <FormField
                    control={form.control}
                    name="base_price"
                    render={({ field: { value, onChange, ...field } }) => (
                      <FormItem>
                        <FormLabel>Base Price (₹) *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter base price"
                            value={value === undefined ? "" : value}
                            onChange={(e) => {
                              const val = e.target.value;
                              onChange(val === "" ? undefined : Number(val));
                            }}
                            {...field}
                            disabled={isSubmitting}
                            className="h-10"
                          />
                        </FormControl>
                        <FormDescription>
                          The regular price of your product or service.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={itemVariants}>
                  <FormField
                    control={form.control}
                    name="discounted_price"
                    render={({ field: { value, onChange, ...field } }) => (
                      <FormItem>
                        <FormLabel>Discounted Price (₹)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter discounted price (optional)"
                            value={value === null ? "" : value}
                            onChange={(e) => {
                              const val = e.target.value;
                              onChange(val === "" ? null : Number(val));
                            }}
                            {...field}
                            disabled={isSubmitting}
                            className="h-10"
                          />
                        </FormControl>
                        <FormDescription>
                          Optional: A special or sale price (must be less than base price).
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div variants={itemVariants} className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="is_available"
                    render={({ field }) => {
                      // Check if enabling this would exceed the plan limit
                      const isAtPlanLimit = planLimit !== undefined &&
                        currentAvailableCount !== undefined &&
                        currentAvailableCount >= planLimit &&
                        !field.value &&
                        !initialData?.is_available;

                      return (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Available</FormLabel>
                            <FormDescription>
                              {isAtPlanLimit
                                ? `You've reached your plan limit of ${planLimit} available products.`
                                : "Toggle whether this product/service is currently available."}
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={isSubmitting || isAtPlanLimit}
                            />
                          </FormControl>
                        </FormItem>
                      );
                    }}
                  />
                </motion.div>

                <motion.div variants={itemVariants} className="md:col-span-2">
                  <FormLabel>Product Images</FormLabel>
                  <ProductMultiImageUpload
                    images={images}
                    featuredImageIndex={featuredImageIndex}
                    onAddImage={addImageSlot}
                    onRemoveImage={removeImage}
                    onSetFeatured={setAsFeatured}
                    onFileSelect={handleFileSelect}
                    disabled={isSubmitting}
                    errorDisplay={imageErrorDisplay}
                  />
                </motion.div>

                {/* Variant Management Section - Available for both new and existing products */}
                <motion.div variants={itemVariants} className="md:col-span-2">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <FormLabel className="text-base font-semibold">Product Variants</FormLabel>
                        <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
                          {isEditing
                            ? "Manage different variations of this product (e.g., size, color, style)"
                            : "Add different variations of this product (e.g., size, color, style). Variants will be saved when you create the product."
                          }
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowVariantManagement(!showVariantManagement)}
                        className="flex items-center gap-2"
                      >
                        <Settings className="h-4 w-4" />
                        {showVariantManagement ? 'Hide' : 'Show'} Variants
                      </Button>
                    </div>

                    {showVariantManagement && (
                      <div className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 bg-white dark:bg-black">
                        <div className="flex flex-col sm:flex-row gap-2 mb-4">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleAddVariant}
                            className="flex items-center gap-2"
                            disabled={showVariantForm}
                          >
                            <Plus className="h-4 w-4" />
                            Add Variant
                          </Button>
                        </div>

                        {/* Inline Variant Form */}
                        {showVariantForm && (
                          <div className="mb-6 p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg bg-white dark:bg-black">
                            <div className="flex items-center justify-between mb-4">
                              <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                                {editingVariant ? "Edit Variant" : "Add New Variant"}
                              </h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={handleCancelVariantForm}
                                className="h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                            <VariantForm
                              productId={initialData?.id || 'draft'}
                              initialData={editingVariant}
                              onSubmit={handleVariantSubmit}
                              isSubmitting={false}
                              onCancel={handleCancelVariantForm}
                            />
                          </div>
                        )}

                        {variants.length > 0 ? (
                          <VariantTable
                            productId={initialData?.id || 'draft'}
                            variants={variants}
                            onAddVariant={handleAddVariant}
                            onEditVariant={handleEditVariant}
                            onDeleteVariant={handleDeleteVariant}
                            onToggleVariantAvailability={handleToggleVariantAvailability}
                          />
                        ) : (
                          <div className="text-center py-8 text-neutral-500 dark:text-neutral-400">
                            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p className="text-sm">No variants created yet</p>
                            <p className="text-xs mt-1">Add variants to offer different options for this product</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>

                {/* Legacy variant section for existing products only */}
                {false && isEditing && initialData && (
                  <motion.div variants={itemVariants} className="md:col-span-2">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <FormLabel className="text-base font-semibold">Product Variants</FormLabel>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
                            Manage different variations of this product (e.g., size, color, style)
                          </p>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setShowVariantManagement(!showVariantManagement)}
                          className="flex items-center gap-2"
                        >
                          <Settings className="h-4 w-4" />
                          {showVariantManagement ? 'Hide' : 'Show'} Variants
                        </Button>
                      </div>

                      {showVariantManagement && (
                        <div className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 bg-white dark:bg-black">
                          <div className="flex flex-col sm:flex-row gap-2 mb-4">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleAddVariant}
                              className="flex items-center gap-2"
                            >
                              <Plus className="h-4 w-4" />
                              Add Variant
                            </Button>

                          </div>

                          {variants.length > 0 ? (
                            <VariantTable
                              productId={initialData?.id || 'draft'}
                              variants={variants}
                              onAddVariant={handleAddVariant}
                              onEditVariant={handleEditVariant}
                              onDeleteVariant={handleDeleteVariant}
                              onToggleVariantAvailability={handleToggleVariantAvailability}
                            />
                          ) : (
                            <div className="text-center py-8 text-neutral-500 dark:text-neutral-400">
                              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                              <p className="text-sm">No variants created yet</p>
                              <p className="text-xs mt-1">Add variants to offer different options for this product</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </div>

              <motion.div variants={itemVariants} className="flex justify-end pt-4 border-t border-neutral-200 dark:border-neutral-800 mt-8">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => window.history.back()}
                  className="mr-2"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <EnhancedGlowButton
                  type="submit"
                  disabled={isSubmitting}
                  className="font-medium"
                  roundedFull
                  size="default"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span>{isEditing ? "Updating..." : "Creating..."}</span>
                    </div>
                  ) : (
                    <>{isEditing ? "Update Product" : "Create Product"}</>
                  )}
                </EnhancedGlowButton>
              </motion.div>
            </form>
          </Form>
        </div>
      </motion.div>

      {/* Image Crop Dialog */}
      <ProductImageCropDialog
        isOpen={!!imageToCrop}
        imgSrc={imageToCrop?.dataUrl || null}
        onCropComplete={handleCropComplete}
        onClose={handleCropDialogClose}
      />




    </div>
  );
}

"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Package, Share2, Home, ZoomIn, Info } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator
} from "@/components/ui/breadcrumb";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi
} from "@/components/ui/carousel";
import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions";
import { ProductVariant } from "@/types/variants";
import WhatsAppButton, { WhatsAppButtonDisabled } from "./WhatsAppButton";
import PhoneButton, { PhoneButtonDisabled } from "./PhoneButton";
import BuyNowButton from "./BuyNowButton";
import ImageZoomModal from "./ImageZoomModal";
import VariantSelector from "./VariantSelector";
import { formatCurrency } from "@/lib/utils";
import { AdData } from "@/types/ad";
import ProductAdSection from "./ProductAdSection";

interface ProductDetailProps {
  product: ProductServiceData;
  variants?: ProductVariant[];
  businessSlug: string;
  businessName: string;
  whatsappNumber: string | null;
  phoneNumber: string | null;
  topAdData: AdData;
  businessCustomAd?: {
    enabled?: boolean;
    image_url?: string;
    link_url?: string;
  } | null;
  userPlan?: "free" | "basic" | "growth" | "pro" | "enterprise";
}

export default function ProductDetail({
  product,
  variants = [],
  businessSlug,
  businessName,
  whatsappNumber,
  phoneNumber,
  topAdData,
  businessCustomAd,
  userPlan,
}: ProductDetailProps) {
  const [imageLoaded, setImageLoaded] = useState<Record<string, boolean>>({});
  const [imageError, setImageError] = useState<Record<string, boolean>>({});
  const [productUrl, setProductUrl] = useState('');
  const [isHovered, setIsHovered] = useState(false);
  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [mainCarouselApi, setMainCarouselApi] = useState<CarouselApi>();
  const [thumbnailCarouselApi, setThumbnailCarouselApi] = useState<CarouselApi>();
  const [allImages, setAllImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Variant state
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [currentProduct, setCurrentProduct] = useState(product);

  // Handle variant selection
  const handleVariantSelect = (variant: ProductVariant) => {
    setSelectedVariant(variant);

    // Update current product data with variant-specific information
    const updatedProduct = {
      ...product,
      base_price: variant.base_price || product.base_price,
      discounted_price: variant.discounted_price || product.discounted_price,
      images: variant.images && variant.images.length > 0 ? variant.images : product.images,
      featured_image_index: variant.images && variant.images.length > 0 ? variant.featured_image_index : product.featured_image_index,
    };

    setCurrentProduct(updatedProduct);
  };

  // Initialize variant selection
  useEffect(() => {
    if (variants && variants.length > 0 && !selectedVariant) {
      // Select the first available variant by default
      const firstAvailableVariant = variants.find(v => v.is_available) || variants[0];
      handleVariantSelect(firstAvailableVariant);
    }
  }, [variants, selectedVariant]);

  // Initialize images and product URL
  useEffect(() => {
    setProductUrl(window.location.href);

    const images: string[] = [];
    if (currentProduct.images && Array.isArray(currentProduct.images)) {
      images.push(...currentProduct.images);
    }
    if (currentProduct.image_url && !images.includes(currentProduct.image_url)) {
      images.push(currentProduct.image_url);
    }
    setAllImages(images);

    if (images.length > 0) {
      const featuredIndex = typeof currentProduct.featured_image_index === 'number'
        ? Math.min(currentProduct.featured_image_index, images.length - 1)
        : 0;
      setCurrentImageIndex(featuredIndex);
      setSelectedImageUrl(images[featuredIndex]);
    }
  }, [currentProduct.images, currentProduct.image_url, currentProduct.featured_image_index]);

  // Sync main carousel with currentImageIndex
  useEffect(() => {
    if (!mainCarouselApi) return;
    mainCarouselApi.scrollTo(currentImageIndex);
  }, [mainCarouselApi, currentImageIndex]);

  // Sync thumbnail carousel with main carousel
  useEffect(() => {
    if (!mainCarouselApi || !thumbnailCarouselApi) return;

    const onSelect = () => {
      const newIndex = mainCarouselApi.selectedScrollSnap();
      setCurrentImageIndex(newIndex);
      setSelectedImageUrl(allImages[newIndex]);
      thumbnailCarouselApi.scrollTo(newIndex);
    };

    mainCarouselApi.on("select", onSelect);
    mainCarouselApi.on("reInit", onSelect); // Re-initialize on reInit

    return () => {
      mainCarouselApi.off("select", onSelect);
      mainCarouselApi.off("reInit", onSelect);
    };
  }, [mainCarouselApi, thumbnailCarouselApi, allImages]);

  // Format prices using current product data (which may include variant pricing)
  const formattedBasePrice = formatCurrency(currentProduct.base_price);
  const formattedDiscountedPrice = currentProduct.discounted_price
    ? formatCurrency(currentProduct.discounted_price)
    : null;

  // Calculate discount percentage if applicable
  let discountPercentage = 0;
  if (
    currentProduct.discounted_price &&
    currentProduct.base_price &&
    currentProduct.discounted_price < currentProduct.base_price
  ) {
    discountPercentage = Math.round(
      ((currentProduct.base_price - currentProduct.discounted_price) / currentProduct.base_price) * 100
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  const imageVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.7,
        ease: "easeOut"
      },
    },
  };

  const detailsVariants = {
    hidden: { opacity: 0, x: 30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.7,
        ease: "easeOut",
        delay: 0.2
      },
    },
  };

  return (
    <motion.div
      className="w-full mx-auto flex flex-col"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Image Zoom Modal */}
      {isZoomModalOpen && selectedImageUrl && (
        <ImageZoomModal
          isOpen={isZoomModalOpen}
          onClose={() => setIsZoomModalOpen(false)}
          imageUrl={selectedImageUrl}
          altText={product.name || "Product image"}
        />
      )}
      {/* Breadcrumb navigation */}
      <motion.div variants={itemVariants} className="mb-5">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">
                <Home className="w-4 h-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${businessSlug}`}>
                {businessName}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{product.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 lg:gap-16">
        {/* Left column - Image Carousel */}
        <motion.div variants={imageVariants} className="relative md:sticky md:top-20 self-start md:px-4">
          {/* Main image display container */}

          {/* Main image display */}
          <Carousel
            className="w-full mb-4"
            opts={{
              align: "start",
              loop: true,
            }}
            setApi={setMainCarouselApi}
          >
            <CarouselContent>
              {allImages.length > 0 ? (
                allImages.map((imageUrl, index) => (
                  <CarouselItem key={index}>
                    <div
                      className="relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer"
                      onClick={() => {
                        if (imageUrl && !imageError[imageUrl]) {
                          setIsZoomModalOpen(true);
                        }
                      }}
                    >
                      {/* Loading skeleton */}
                      {!imageLoaded[imageUrl] && !imageError[imageUrl] && (
                        <Skeleton className="absolute inset-0" />
                      )}

                      {/* Image display */}
                      {!imageError[imageUrl] ? (
                        <>
                          <Image
                            src={imageUrl}
                            alt={product.name}
                            fill
                            className={`object-cover transition-all duration-500 ${
                              imageLoaded[imageUrl] ? "opacity-100" : "opacity-0"
                            } group-hover:scale-105`}
                            onLoad={() => setImageLoaded(prev => ({ ...prev, [imageUrl]: true }))}
                            onError={() => setImageError(prev => ({ ...prev, [imageUrl]: true }))}
                            sizes="(max-width: 768px) 100vw, 50vw"
                            priority
                          />
                          {/* Zoom indicator */}
                          {imageLoaded[imageUrl] && (
                            <div
                              className="absolute bottom-3 right-3 bg-black/50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer"
                            >
                              <ZoomIn className="w-5 h-5" />
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Package className="w-20 h-20 text-neutral-300 dark:text-neutral-700" />
                        </div>
                      )}

                      {/* Discount badge */}
                      {discountPercentage > 0 && (
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ type: "spring", stiffness: 400, damping: 10 }}
                          className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1.5 rounded-lg font-bold text-sm shadow-lg"
                        >
                          <div className="flex flex-col items-center justify-center">
                            <span className="text-xs font-medium sm:text-[0.6rem]">SAVE</span>
                            <span className="text-sm leading-none sm:text-xs">{discountPercentage}%</span>
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </CarouselItem>
                ))
              ) : (
                <CarouselItem>
                  <div className="relative w-full aspect-square overflow-hidden bg-neutral-50 dark:bg-neutral-900 group rounded-xl border border-neutral-200 dark:border-neutral-800 cursor-pointer mb-4 flex items-center justify-center">
                    <Package className="w-20 h-20 text-neutral-300 dark:text-neutral-700" />
                  </div>
                </CarouselItem>
              )}
            </CarouselContent>
          </Carousel>

          {/* Image carousel for multiple images */}
          {allImages.length > 1 && (
            <Carousel
              className="w-full"
              opts={{
                align: "start",
                loop: true,
              }}
              setApi={setThumbnailCarouselApi}
            >
              <CarouselContent className="justify-center">
                {allImages.map((imageUrl, index) => (
                  <CarouselItem key={index} className="basis-1/4 md:basis-1/5 lg:basis-1/6">
                    <div
                      className={`relative aspect-square overflow-hidden rounded-md cursor-pointer border-2 ${
                        selectedImageUrl === imageUrl
                          ? 'border-[var(--brand-gold)]'
                          : 'border-transparent'
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <Image
                        src={imageUrl}
                        alt={`${product.name || "Product"} - Image ${index + 1}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 25vw, 10vw"
                        onLoad={() => setImageLoaded(prev => ({ ...prev, [imageUrl]: true }))}
                        onError={() => setImageError(prev => ({ ...prev, [imageUrl]: true }))}
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          )}
        </motion.div>

        {/* Right column - Details */}
        <motion.div variants={detailsVariants} className="flex flex-col space-y-5 p-6 md:p-8 lg:p-10 md:sticky md:top-20 self-start md:px-4">

          {/* Product name */}
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 leading-tight">
            {product.name}
          </h1>

          {/* Product/Service Badge */}
          <Badge variant="secondary" className="w-fit capitalize">
            {product.product_type}
          </Badge>

          {/* Price section */}
          <div className="flex items-baseline gap-3">
            {formattedDiscountedPrice ? (
              <>
                <span className="text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50">
                  {formattedDiscountedPrice}
                </span>
                <span className="text-sm md:text-base line-through text-neutral-500 dark:text-neutral-400">
                  {formattedBasePrice}
                </span>
              </>
            ) : (
              <span className="text-2xl md:text-3xl font-bold text-neutral-900 dark:text-neutral-50">
                {formattedBasePrice}
              </span>
            )}
          </div>

          {/* Price Disclaimer Message */}
          <Alert variant="default" className="mt-4 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200">
            <Info className="h-4 w-4" />
            <AlertTitle>Important Price Information</AlertTitle>
            <AlertDescription>
              Prices are indicative and may vary in-store. Visit us or contact directly for final deals and confirmation.
            </AlertDescription>
          </Alert>

          {/* Variant Selector */}
          {variants && variants.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mt-6"
            >
              <VariantSelector
                variants={variants}
                selectedVariant={selectedVariant}
                onVariantSelect={handleVariantSelect}
                className="p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg"
              />
            </motion.div>
          )}

          {/* Description */}
          {product.description && (
            <div className="mt-6 text-neutral-700 dark:text-neutral-300">
              <h2 className="text-lg font-semibold mb-2">Description</h2>
              <p className="whitespace-pre-line leading-relaxed">{product.description}</p>
            </div>
          )}

          {/* Action buttons */}
          <div className="mt-6 pt-5 border-t border-neutral-200 dark:border-neutral-800 space-y-4">
            {/* Buy Now button (disabled with coming soon message) */}
            <BuyNowButton />

            {/* WhatsApp and Phone buttons in two columns on desktop */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* WhatsApp button */}
              <div>
                {whatsappNumber ? (
                  <WhatsAppButton
                    whatsappNumber={whatsappNumber}
                    productName={product.name}
                    businessName={businessName}
                    productUrl={productUrl || `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}/product/${product.slug || product.id}`}
                  />
                ) : (
                  <WhatsAppButtonDisabled />
                )}
              </div>

              {/* Phone button */}
              <div>
                {phoneNumber ? (
                  <PhoneButton
                    phoneNumber={phoneNumber}
                    _businessName={businessName}
                  />
                ) : (
                  <PhoneButtonDisabled />
                )}
              </div>
            </div>
          </div>

          {/* Share button */}
          <div className="mt-4 relative group">
            {/* Button glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-neutral-500/20 to-neutral-600/20 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <Button
              variant="outline"
              className="relative w-full flex items-center justify-center gap-3 py-5 border border-neutral-300/50 dark:border-neutral-700/50 hover:border-[var(--brand-gold)]/50 dark:hover:border-[var(--brand-gold)]/50 rounded-xl transition-all duration-300 bg-white/50 dark:bg-black/50 backdrop-blur-sm"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={() => {
                const shareUrl = productUrl || `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}/product/${product.slug || product.id}`;

                // Create variant-specific sharing text
                let shareText = `Check out ${product.name} from ${businessName} on Dukancard`;
                if (selectedVariant && variants.length > 0) {
                  const variantInfo = Object.entries(selectedVariant.variant_values)
                    .map(([type, value]) => `${type}: ${value}`)
                    .join(', ');
                  shareText = `Check out ${product.name} (${variantInfo}) from ${businessName} on Dukancard`;
                }

                if (navigator.share) {
                  navigator.share({
                    title: product.name,
                    text: shareText,
                    url: shareUrl,
                  });
                } else {
                  navigator.clipboard.writeText(shareUrl);
                  alert("Link copied to clipboard!");
                }
              }}
            >
              <Share2 className={`w-5 h-5 text-[var(--brand-gold)] transition-all duration-300 ${isHovered ? 'rotate-12' : ''}`} />
              <span className="text-base font-medium tracking-wide">Share</span>
            </Button>
          </div>
        </motion.div>
      </div>

      {/* Custom Ad Section - Added below product details */}
      <motion.div
        variants={itemVariants}
        className="mt-8 md:mt-12"
      >
        <ProductAdSection
          topAdData={topAdData}
          itemVariants={itemVariants}
          businessCustomAd={businessCustomAd}
          userPlan={userPlan}
        />
      </motion.div>
    </motion.div>
  );
}

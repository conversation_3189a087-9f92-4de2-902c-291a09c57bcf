// Utility functions for product variants operations and validation

import { ProductVariant, VariantCombination, VARIANT_CONSTRAINTS } from "@/types/variants";
import { ProductWithVariantInfo } from "@/types/products";

// Validation utilities
export const variantValidation = {
  /**
   * Validates if a variant name is valid
   */
  isValidVariantName: (name: string): boolean => {
    return name.trim().length > 0 && name.length <= VARIANT_CONSTRAINTS.MAX_VARIANT_NAME_LENGTH;
  },

  /**
   * Validates if variant values object is valid
   */
  isValidVariantValues: (values: Record<string, string>): boolean => {
    const keys = Object.keys(values);
    return (
      keys.length > 0 &&
      keys.length <= VARIANT_CONSTRAINTS.MAX_VARIANT_TYPES_PER_PRODUCT &&
      keys.every(key => key.trim().length > 0) &&
      Object.values(values).every(value => value.trim().length > 0)
    );
  },

  /**
   * Validates if a product can have more variants
   */
  canAddMoreVariants: (currentVariantCount: number): boolean => {
    return currentVariantCount < VARIANT_CONSTRAINTS.MAX_VARIANTS_PER_PRODUCT;
  },

  /**
   * Validates if variant combination is unique within a product
   */
  isUniqueVariantCombination: (
    variants: ProductVariant[],
    newVariantValues: Record<string, string>,
    excludeVariantId?: string
  ): boolean => {
    const newCombinationString = JSON.stringify(newVariantValues);
    return !variants.some(variant => 
      variant.id !== excludeVariantId && 
      JSON.stringify(variant.variant_values) === newCombinationString
    );
  },

  /**
   * Validates pricing constraints
   */
  isValidPricing: (basePrice?: number | null, discountedPrice?: number | null): boolean => {
    if (basePrice !== null && basePrice !== undefined && basePrice < 0) return false;
    if (discountedPrice !== null && discountedPrice !== undefined && discountedPrice < 0) return false;
    if (
      basePrice !== null && basePrice !== undefined &&
      discountedPrice !== null && discountedPrice !== undefined &&
      discountedPrice >= basePrice
    ) return false;
    return true;
  },
};

// Variant combination utilities
export const variantCombinations = {
  /**
   * Generates all possible combinations from variant types and values
   */
  generateCombinations: (
    variantTypesValues: Record<string, string[]>
  ): VariantCombination[] => {
    const keys = Object.keys(variantTypesValues);
    if (keys.length === 0) return [];

    const values = keys.map(key => variantTypesValues[key]);
    
    function cartesianProduct(arrays: string[][]): string[][] {
      return arrays.reduce((acc, curr) => 
        acc.flatMap(a => curr.map(c => [...a, c])), 
        [[]] as string[][]
      );
    }

    const combinations = cartesianProduct(values);
    
    return combinations.map(combination => {
      const variantValues: Record<string, string> = {};
      keys.forEach((key, index) => {
        variantValues[key] = combination[index];
      });
      
      const combinationName = combination.join(' ');
      
      return {
        combination: variantValues,
        combination_name: combinationName,
      };
    });
  },

  /**
   * Filters out existing combinations from generated combinations
   */
  filterExistingCombinations: (
    generatedCombinations: VariantCombination[],
    existingVariants: ProductVariant[]
  ): VariantCombination[] => {
    const existingCombinations = existingVariants.map(variant => 
      JSON.stringify(variant.variant_values)
    );

    return generatedCombinations.filter(combo => 
      !existingCombinations.includes(JSON.stringify(combo.combination))
    );
  },

  /**
   * Creates variant name from combination values
   */
  createVariantName: (variantValues: Record<string, string>): string => {
    return Object.values(variantValues).join(' ');
  },
};

// Variant display utilities
export const variantDisplay = {
  /**
   * Formats variant values for display
   */
  formatVariantValues: (variantValues: Record<string, string>): string => {
    return Object.entries(variantValues)
      .map(([type, value]) => `${type}: ${value}`)
      .join(', ');
  },

  /**
   * Gets display price for a variant (discounted price if available, otherwise base price)
   */
  getDisplayPrice: (variant: ProductVariant): number | null => {
    return variant.discounted_price ?? variant.base_price ?? null;
  },

  /**
   * Checks if variant has a discount
   */
  hasDiscount: (variant: ProductVariant): boolean => {
    return !!(
      variant.base_price && 
      variant.discounted_price && 
      variant.discounted_price < variant.base_price
    );
  },

  /**
   * Calculates discount percentage
   */
  getDiscountPercentage: (variant: ProductVariant): number | null => {
    if (!variantDisplay.hasDiscount(variant) || !variant.base_price || !variant.discounted_price) {
      return null;
    }
    return Math.round(((variant.base_price - variant.discounted_price) / variant.base_price) * 100);
  },

  /**
   * Gets the featured image URL for a variant
   */
  getFeaturedImageUrl: (variant: ProductVariant): string | null => {
    if (!variant.images || variant.images.length === 0) return null;
    const index = Math.min(variant.featured_image_index, variant.images.length - 1);
    return variant.images[index] || variant.images[0] || null;
  },

  /**
   * Creates a summary string for variant selection
   */
  getVariantSummary: (variants: ProductVariant[]): string => {
    if (variants.length === 0) return "No variants";
    
    const availableCount = variants.filter(v => v.is_available).length;
    const totalCount = variants.length;
    
    if (availableCount === totalCount) {
      return `${totalCount} variant${totalCount === 1 ? '' : 's'}`;
    } else {
      return `${availableCount}/${totalCount} variant${totalCount === 1 ? '' : 's'} available`;
    }
  },
};

// Product utilities with variant support
export const productVariantUtils = {
  /**
   * Gets the best price for a product (considering variants)
   */
  getBestPrice: (product: ProductWithVariantInfo, variants?: ProductVariant[]): number | null => {
    const productPrice = variantDisplay.getDisplayPrice(product as any);
    
    if (!variants || variants.length === 0) {
      return productPrice;
    }

    const availableVariants = variants.filter(v => v.is_available);
    if (availableVariants.length === 0) {
      return productPrice;
    }

    const variantPrices = availableVariants
      .map(v => variantDisplay.getDisplayPrice(v))
      .filter((price): price is number => price !== null);

    if (variantPrices.length === 0) {
      return productPrice;
    }

    const minVariantPrice = Math.min(...variantPrices);
    
    if (productPrice === null) {
      return minVariantPrice;
    }

    return Math.min(productPrice, minVariantPrice);
  },

  /**
   * Gets price range for a product with variants
   */
  getPriceRange: (product: ProductWithVariantInfo, variants?: ProductVariant[]): { min: number | null; max: number | null } => {
    const productPrice = variantDisplay.getDisplayPrice(product as any);
    
    if (!variants || variants.length === 0) {
      return { min: productPrice, max: productPrice };
    }

    const availableVariants = variants.filter(v => v.is_available);
    const variantPrices = availableVariants
      .map(v => variantDisplay.getDisplayPrice(v))
      .filter((price): price is number => price !== null);

    const allPrices = [
      ...(productPrice !== null ? [productPrice] : []),
      ...variantPrices
    ];

    if (allPrices.length === 0) {
      return { min: null, max: null };
    }

    return {
      min: Math.min(...allPrices),
      max: Math.max(...allPrices),
    };
  },

  /**
   * Checks if a product has available variants
   */
  hasAvailableVariants: (variants: ProductVariant[]): boolean => {
    return variants.some(v => v.is_available);
  },

  /**
   * Gets variant types used in a product
   */
  getVariantTypes: (variants: ProductVariant[]): string[] => {
    const typesSet = new Set<string>();
    variants.forEach(variant => {
      Object.keys(variant.variant_values).forEach(type => typesSet.add(type));
    });
    return Array.from(typesSet).sort();
  },

  /**
   * Gets all possible values for a variant type in a product
   */
  getVariantTypeValues: (variants: ProductVariant[], variantType: string): string[] => {
    const valuesSet = new Set<string>();
    variants.forEach(variant => {
      const value = variant.variant_values[variantType];
      if (value) valuesSet.add(value);
    });
    return Array.from(valuesSet).sort();
  },
};

// Formatting utilities
export const formatters = {
  /**
   * Formats price with currency
   */
  formatPrice: (price: number | null | undefined, currency: string = "₹"): string => {
    if (price === null || price === undefined) return "Price not set";
    return `${currency}${price.toLocaleString()}`;
  },

  /**
   * Formats price range
   */
  formatPriceRange: (min: number | null, max: number | null, currency: string = "₹"): string => {
    if (min === null && max === null) return "Price not set";
    if (min === max) return formatters.formatPrice(min, currency);
    return `${formatters.formatPrice(min, currency)} - ${formatters.formatPrice(max, currency)}`;
  },

  /**
   * Formats variant count
   */
  formatVariantCount: (count: number): string => {
    if (count === 0) return "No variants";
    return `${count} variant${count === 1 ? '' : 's'}`;
  },
};

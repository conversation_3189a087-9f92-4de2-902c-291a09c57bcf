"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Wand2,
  Plus,
  X,
  Check,
  Package,
  Shuffle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { variantCombinations } from "@/lib/utils/variantHelpers";

interface VariantType {
  name: string;
  values: string[];
}

interface GeneratedVariant {
  id: string;
  variant_name: string;
  variant_values: Record<string, string>;
  selected: boolean;
}

interface VariantCombinationGeneratorProps {
  onGenerateVariants: (_variants: Omit<GeneratedVariant, 'id' | 'selected'>[]) => Promise<void>;
  existingVariants?: { variant_values: Record<string, string> }[];
  disabled?: boolean;
  className?: string;
}

export default function VariantCombinationGenerator({
  onGenerateVariants,
  existingVariants = [],
  disabled = false,
  className,
}: VariantCombinationGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [variantTypes, setVariantTypes] = useState<VariantType[]>([]);
  const [generatedVariants, setGeneratedVariants] = useState<GeneratedVariant[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addVariantType = () => {
    setVariantTypes(prev => [
      ...prev,
      { name: "", values: [""] }
    ]);
  };

  const removeVariantType = (index: number) => {
    setVariantTypes(prev => prev.filter((_, i) => i !== index));
  };

  const updateVariantTypeName = (index: number, name: string) => {
    setVariantTypes(prev => prev.map((type, i) => 
      i === index ? { ...type, name } : type
    ));
  };

  const addVariantValue = (typeIndex: number) => {
    setVariantTypes(prev => prev.map((type, i) => 
      i === typeIndex 
        ? { ...type, values: [...type.values, ""] }
        : type
    ));
  };

  const removeVariantValue = (typeIndex: number, valueIndex: number) => {
    setVariantTypes(prev => prev.map((type, i) => 
      i === typeIndex 
        ? { ...type, values: type.values.filter((_, vi) => vi !== valueIndex) }
        : type
    ));
  };

  const updateVariantValue = (typeIndex: number, valueIndex: number, value: string) => {
    setVariantTypes(prev => prev.map((type, i) => 
      i === typeIndex 
        ? { 
            ...type, 
            values: type.values.map((v, vi) => vi === valueIndex ? value : v)
          }
        : type
    ));
  };

  const generateCombinations = () => {
    setIsGenerating(true);
    
    try {
      // Validate input
      const validTypes = variantTypes.filter(type => 
        type.name.trim() && type.values.some(v => v.trim())
      );

      if (validTypes.length === 0) {
        toast.error("Please add at least one variant type with values");
        return;
      }

      // Clean up types and values and convert to the expected format
      const variantTypesValues: Record<string, string[]> = {};
      validTypes.forEach(type => {
        const cleanName = type.name.trim();
        const cleanValues = type.values.filter(v => v.trim()).map(v => v.trim());
        if (cleanName && cleanValues.length > 0) {
          variantTypesValues[cleanName] = cleanValues;
        }
      });

      // Generate all combinations
      const combinations = variantCombinations.generateCombinations(variantTypesValues);

      // Filter out existing combinations
      const existingCombinationsSet = new Set(
        existingVariants.map(variant =>
          JSON.stringify(variant.variant_values)
        )
      );

      const newCombinations = combinations.filter((combo: any) =>
        !existingCombinationsSet.has(JSON.stringify(combo.combination))
      );

      if (newCombinations.length === 0) {
        toast.warning("All possible combinations already exist as variants");
        setGeneratedVariants([]);
        return;
      }

      // Convert to GeneratedVariant format
      const generatedVariants: GeneratedVariant[] = newCombinations.map((combo: any, index: number) => ({
        id: `generated-${index}`,
        variant_name: combo.combination_name,
        variant_values: combo.combination,
        selected: true, // Select all by default
      }));

      setGeneratedVariants(generatedVariants);
      toast.success(`Generated ${generatedVariants.length} new variant combinations`);

    } catch (error) {
      console.error("Error generating combinations:", error);
      toast.error("Failed to generate variant combinations");
    } finally {
      setIsGenerating(false);
    }
  };

  const toggleVariantSelection = (variantId: string) => {
    setGeneratedVariants(prev => prev.map(variant => 
      variant.id === variantId 
        ? { ...variant, selected: !variant.selected }
        : variant
    ));
  };

  const selectAll = () => {
    setGeneratedVariants(prev => prev.map(variant => ({ ...variant, selected: true })));
  };

  const selectNone = () => {
    setGeneratedVariants(prev => prev.map(variant => ({ ...variant, selected: false })));
  };

  const handleSubmit = async () => {
    const selectedVariants = generatedVariants.filter(v => v.selected);
    
    if (selectedVariants.length === 0) {
      toast.error("Please select at least one variant to create");
      return;
    }

    setIsSubmitting(true);
    try {
      const variantsToCreate = selectedVariants.map(({ id: _id, selected: _selected, ...variant }) => variant);
      await onGenerateVariants(variantsToCreate);
      
      // Reset state
      setVariantTypes([]);
      setGeneratedVariants([]);
      setIsOpen(false);
      
      toast.success(`Successfully created ${selectedVariants.length} variant${selectedVariants.length > 1 ? 's' : ''}`);
    } catch (error) {
      console.error("Error creating variants:", error);
      toast.error("Failed to create variants. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setVariantTypes([]);
    setGeneratedVariants([]);
  };

  const selectedCount = generatedVariants.filter(v => v.selected).length;
  const canGenerate = variantTypes.some(type => 
    type.name.trim() && type.values.some(v => v.trim())
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn("gap-2", className)}
        >
          <Wand2 className="h-4 w-4" />
          Generate Variants
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shuffle className="h-5 w-5" />
            Variant Combination Generator
          </DialogTitle>
          <DialogDescription>
            Define variant types and their possible values to automatically generate all combinations.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col gap-4">
          {/* Variant Types Configuration */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Variant Types</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVariantType}
                disabled={variantTypes.length >= 5}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Type
              </Button>
            </div>

            <ScrollArea className="max-h-48">
              <div className="space-y-3">
                {variantTypes.map((type, typeIndex) => (
                  <motion.div
                    key={typeIndex}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-3 border rounded-lg space-y-2"
                  >
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Type name (e.g., Color, Size)"
                        value={type.name}
                        onChange={(e) => updateVariantTypeName(typeIndex, e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVariantType(typeIndex)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-xs text-neutral-600">Values</Label>
                      {type.values.map((value, valueIndex) => (
                        <div key={valueIndex} className="flex items-center gap-2">
                          <Input
                            placeholder="Value (e.g., Red, Large)"
                            value={value}
                            onChange={(e) => updateVariantValue(typeIndex, valueIndex, e.target.value)}
                            className="flex-1 text-sm"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeVariantValue(typeIndex, valueIndex)}
                            disabled={type.values.length <= 1}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => addVariantValue(typeIndex)}
                        disabled={type.values.length >= 10}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add Value
                      </Button>
                    </div>
                  </motion.div>
                ))}

                {variantTypes.length === 0 && (
                  <div className="text-center py-8 text-neutral-500">
                    <Package className="h-8 w-8 mx-auto mb-2 text-neutral-400" />
                    <p className="text-sm">No variant types added yet.</p>
                    <p className="text-xs">Click &quot;Add Type&quot; to start creating variant combinations.</p>
                  </div>
                )}
              </div>
            </ScrollArea>

            <div className="flex gap-2">
              <Button
                onClick={generateCombinations}
                disabled={!canGenerate || isGenerating}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Shuffle className="h-4 w-4" />
                    </motion.div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Shuffle className="h-4 w-4 mr-2" />
                    Generate Combinations
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Generated Variants Preview */}
          <AnimatePresence>
            {generatedVariants.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-3"
              >
                <Separator />
                
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">
                    Generated Variants ({generatedVariants.length})
                  </h3>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={selectAll}
                    >
                      Select All
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={selectNone}
                    >
                      Select None
                    </Button>
                  </div>
                </div>

                <ScrollArea className="max-h-64">
                  <div className="space-y-2">
                    {generatedVariants.map((variant) => (
                      <div
                        key={variant.id}
                        className={cn(
                          "flex items-center gap-3 p-3 border rounded-lg transition-colors",
                          variant.selected
                            ? "bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                            : "bg-white dark:bg-neutral-900"
                        )}
                      >
                        <Checkbox
                          checked={variant.selected}
                          onCheckedChange={() => toggleVariantSelection(variant.id)}
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm text-neutral-900 dark:text-neutral-100 truncate">
                            {variant.variant_name}
                          </div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {Object.entries(variant.variant_values).map(([type, value]) => (
                              <Badge key={`${type}-${value}`} variant="outline" className="text-xs">
                                {type}: {value}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                {selectedCount > 0 && (
                  <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                    <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <span className="text-sm text-green-800 dark:text-green-200">
                      {selectedCount} variant{selectedCount > 1 ? 's' : ''} selected for creation
                    </span>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={selectedCount === 0 || isSubmitting}
            className="flex-1 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]"
          >
            {isSubmitting ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <Package className="h-4 w-4" />
                </motion.div>
                Creating {selectedCount} Variant{selectedCount > 1 ? 's' : ''}...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Create {selectedCount} Variant{selectedCount > 1 ? 's' : ''}
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

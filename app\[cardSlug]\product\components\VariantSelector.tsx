"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { ProductVariant } from "@/types/variants";
import { getPredefinedOptionsForType } from "@/lib/constants/predefinedVariants";
import { cn } from "@/lib/utils";

interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant | null;
  onVariantSelect: (variant: ProductVariant) => void;
  className?: string;
  disabled?: boolean;
}

interface VariantOption {
  type: string;
  value: string;
  display_value: string;
  color_code?: string;
  available: boolean;
  variants: ProductVariant[];
}

export default function VariantSelector({
  variants,
  selectedVariant,
  onVariantSelect,
  className,
  disabled = false,
}: VariantSelectorProps) {
  const [variantTypes, setVariantTypes] = useState<Record<string, VariantOption[]>>({});
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});

  // Process variants to extract variant types and options
  useEffect(() => {
    if (!variants || variants.length === 0) return;

    const types: Record<string, Set<string>> = {};
    const typeOptions: Record<string, VariantOption[]> = {};

    // Extract all variant types and their possible values from actual variants
    variants.forEach(variant => {
      Object.entries(variant.variant_values).forEach(([type, value]) => {
        if (!types[type]) {
          types[type] = new Set();
        }
        types[type].add(value);
      });
    });

    // Create variant options for each type, only showing values that exist in variants
    Object.entries(types).forEach(([type, values]) => {
      const predefinedOptions = getPredefinedOptionsForType(type);

      typeOptions[type] = Array.from(values).map(value => {
        const variantsWithThisOption = variants.filter(variant =>
          variant.variant_values[type] === value
        );

        // Find predefined option for display value and color code
        const predefinedOption = predefinedOptions.find(opt => opt.value === value);

        return {
          type,
          value,
          display_value: predefinedOption?.display_value || value,
          color_code: predefinedOption?.color_code,
          available: variantsWithThisOption.some(v => v.is_available),
          variants: variantsWithThisOption,
        };
      }).sort((a, b) => {
        // Sort by predefined sort order if available
        const aPredefined = predefinedOptions.find(opt => opt.value === a.value);
        const bPredefined = predefinedOptions.find(opt => opt.value === b.value);
        if (aPredefined && bPredefined) {
          return aPredefined.sort_order - bPredefined.sort_order;
        }
        return a.display_value.localeCompare(b.display_value);
      });
    });

    setVariantTypes(typeOptions);

    // Don't auto-select any options - let user make their own choices
    // This ensures all available options are visible initially
  }, [variants, selectedOptions]);

  // Update selected variant when options change
  useEffect(() => {
    if (Object.keys(selectedOptions).length === 0) return;

    const matchingVariant = variants.find(variant => {
      return Object.entries(selectedOptions).every(([type, value]) => 
        variant.variant_values[type] === value
      );
    });

    if (matchingVariant && matchingVariant !== selectedVariant) {
      onVariantSelect(matchingVariant);
    }
  }, [selectedOptions, variants, selectedVariant, onVariantSelect]);

  // Handle option selection
  const handleOptionSelect = (type: string, value: string) => {
    if (disabled) return;

    setSelectedOptions(prev => ({
      ...prev,
      [type]: value,
    }));
  };

  // Check if an option is available given current selections (progressive filtering)
  const isOptionAvailable = (type: string, value: string): boolean => {
    // If no selections have been made yet, show all options that exist in any available variant
    if (Object.keys(selectedOptions).length === 0) {
      return variants.some(variant =>
        variant.is_available && variant.variant_values[type] === value
      );
    }

    // If selections have been made, use progressive filtering
    const testSelection = { ...selectedOptions, [type]: value };

    // Check if any available variant can satisfy this partial selection
    return variants.some(variant => {
      if (!variant.is_available) return false;

      // Check if this variant matches all currently selected options plus the new one
      return Object.entries(testSelection).every(([testType, testValue]) => {
        return variant.variant_values[testType] === testValue;
      });
    });
  };

  if (!variants || variants.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-6", className)}>
      {Object.entries(variantTypes).map(([type, options]) => (
        <motion.div
          key={type}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-3"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-neutral-900 dark:text-neutral-100 capitalize">
              {type}
            </h3>
            {selectedOptions[type] && (
              <Badge variant="secondary" className="text-xs font-medium">
                {options.find(opt => opt.value === selectedOptions[type])?.display_value || selectedOptions[type]}
              </Badge>
            )}
          </div>

          <div className="flex flex-wrap gap-3">
            {options.map((option) => {
              const isSelected = selectedOptions[type] === option.value;
              const isAvailable = isOptionAvailable(type, option.value);
              const isColorType = type.toLowerCase() === 'color';

              return (
                <motion.div
                  key={`${type}-${option.value}`}
                  whileHover={isAvailable && !disabled ? { scale: 1.05 } : {}}
                  whileTap={isAvailable && !disabled ? { scale: 0.95 } : {}}
                  className="relative"
                >
                  {isColorType && option.color_code ? (
                    // Color swatch for color variants
                    <button
                      onClick={() => isAvailable && handleOptionSelect(type, option.value)}
                      disabled={!isAvailable || disabled}
                      className={cn(
                        "relative w-12 h-12 rounded-full transition-all duration-200 border-2",
                        "border-neutral-300 dark:border-neutral-600",
                        isSelected && "ring-2 ring-[var(--brand-gold)] ring-offset-2 ring-offset-white dark:ring-offset-neutral-900",
                        !isAvailable && "opacity-60 cursor-not-allowed",
                        isAvailable && "cursor-pointer hover:scale-105"
                      )}
                      style={{ backgroundColor: option.color_code }}
                      title={option.display_value}
                    >
                      <AnimatePresence>
                        {isSelected && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className="absolute inset-0 flex items-center justify-center"
                          >
                            <Check className={cn(
                              "h-5 w-5 drop-shadow-sm",
                              // Use white check for dark colors, dark check for light colors
                              option.color_code === '#FFFFFF' || option.color_code === '#FFFF00' || option.color_code === '#FFC0CB'
                                ? "text-neutral-800"
                                : "text-white"
                            )} />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </button>
                  ) : (
                    // Regular button for non-color variants
                    <button
                      onClick={() => isAvailable && handleOptionSelect(type, option.value)}
                      disabled={!isAvailable || disabled}
                      className={cn(
                        "relative px-4 py-2.5 rounded-lg transition-all duration-200 font-medium text-sm",
                        isSelected && [
                          "bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)]",
                          "ring-2 ring-[var(--brand-gold)]/30"
                        ],
                        !isAvailable && [
                          "opacity-60 cursor-not-allowed",
                          "text-neutral-400 dark:text-neutral-600"
                        ],
                        isAvailable && !isSelected && [
                          "text-neutral-700 dark:text-neutral-300",
                          "hover:scale-105"
                        ],
                        isAvailable && "cursor-pointer"
                      )}
                    >
                      <span className="flex items-center gap-2">
                        {option.display_value}
                        <AnimatePresence>
                          {isSelected && (
                            <motion.div
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              exit={{ scale: 0, opacity: 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <Check className="h-4 w-4" />
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </span>
                    </button>
                  )}
                </motion.div>
              );
            })}
          </div>

        </motion.div>
      ))}
    </div>
  );
}

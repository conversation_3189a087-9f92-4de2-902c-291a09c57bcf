"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check, Package } from "lucide-react";
import { ProductVariant } from "@/types/variants";
import { cn } from "@/lib/utils";

interface VariantSelectorProps {
  variants: ProductVariant[];
  selectedVariant?: ProductVariant | null;
  onVariantSelect: (variant: ProductVariant) => void;
  className?: string;
  disabled?: boolean;
}

interface VariantOption {
  type: string;
  value: string;
  available: boolean;
  variants: ProductVariant[];
}

export default function VariantSelector({
  variants,
  selectedVariant,
  onVariantSelect,
  className,
  disabled = false,
}: VariantSelectorProps) {
  const [variantTypes, setVariantTypes] = useState<Record<string, VariantOption[]>>({});
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string>>({});

  // Process variants to extract variant types and options
  useEffect(() => {
    if (!variants || variants.length === 0) return;

    const types: Record<string, Set<string>> = {};
    const typeOptions: Record<string, VariantOption[]> = {};

    // Extract all variant types and their possible values
    variants.forEach(variant => {
      Object.entries(variant.variant_values).forEach(([type, value]) => {
        if (!types[type]) {
          types[type] = new Set();
        }
        types[type].add(value);
      });
    });

    // Create variant options for each type
    Object.entries(types).forEach(([type, values]) => {
      typeOptions[type] = Array.from(values).map(value => {
        const variantsWithThisOption = variants.filter(variant => 
          variant.variant_values[type] === value
        );
        
        return {
          type,
          value,
          available: variantsWithThisOption.some(v => v.is_available),
          variants: variantsWithThisOption,
        };
      });
    });

    setVariantTypes(typeOptions);

    // Initialize selected options with the first available option for each type
    if (Object.keys(selectedOptions).length === 0) {
      const initialSelection: Record<string, string> = {};
      Object.entries(typeOptions).forEach(([type, options]) => {
        const availableOption = options.find(opt => opt.available);
        if (availableOption) {
          initialSelection[type] = availableOption.value;
        }
      });
      setSelectedOptions(initialSelection);
    }
  }, [variants, selectedOptions]);

  // Update selected variant when options change
  useEffect(() => {
    if (Object.keys(selectedOptions).length === 0) return;

    const matchingVariant = variants.find(variant => {
      return Object.entries(selectedOptions).every(([type, value]) => 
        variant.variant_values[type] === value
      );
    });

    if (matchingVariant && matchingVariant !== selectedVariant) {
      onVariantSelect(matchingVariant);
    }
  }, [selectedOptions, variants, selectedVariant, onVariantSelect]);

  // Handle option selection
  const handleOptionSelect = (type: string, value: string) => {
    if (disabled) return;

    setSelectedOptions(prev => ({
      ...prev,
      [type]: value,
    }));
  };

  // Check if an option is available given current selections
  const isOptionAvailable = (type: string, value: string): boolean => {
    const testSelection = { ...selectedOptions, [type]: value };
    
    return variants.some(variant => {
      const matches = Object.entries(testSelection).every(([testType, testValue]) => 
        variant.variant_values[testType] === testValue
      );
      return matches && variant.is_available;
    });
  };

  if (!variants || variants.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-6", className)}>
      {Object.entries(variantTypes).map(([type, options]) => (
        <motion.div
          key={type}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-3"
        >
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 capitalize">
              {type}
            </h3>
            <Badge variant="outline" className="text-xs">
              {selectedOptions[type] || "Select"}
            </Badge>
          </div>

          <div className="flex flex-wrap gap-2">
            {options.map((option) => {
              const isSelected = selectedOptions[type] === option.value;
              const isAvailable = isOptionAvailable(type, option.value);
              
              return (
                <motion.div
                  key={`${type}-${option.value}`}
                  whileHover={isAvailable && !disabled ? { scale: 1.02 } : {}}
                  whileTap={isAvailable && !disabled ? { scale: 0.98 } : {}}
                >
                  <Button
                    variant={isSelected ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleOptionSelect(type, option.value)}
                    disabled={!isAvailable || disabled}
                    className={cn(
                      "relative h-10 px-4 transition-all duration-200",
                      isSelected && [
                        "bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)]",
                        "text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]"
                      ],
                      !isAvailable && [
                        "opacity-50 cursor-not-allowed",
                        "bg-neutral-100 dark:bg-neutral-800",
                        "text-neutral-400 dark:text-neutral-600"
                      ],
                      isAvailable && !isSelected && [
                        "hover:border-[var(--brand-gold)]/50",
                        "hover:bg-[var(--brand-gold)]/5"
                      ]
                    )}
                  >
                    <span className="flex items-center gap-2">
                      {option.value}
                      <AnimatePresence>
                        {isSelected && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            exit={{ scale: 0, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <Check className="h-3 w-3" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </span>
                    
                    {!isAvailable && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-full h-0.5 bg-neutral-400 dark:bg-neutral-600 rotate-45" />
                      </div>
                    )}
                  </Button>
                </motion.div>
              );
            })}
          </div>

          {/* Availability indicator */}
          <div className="text-xs text-neutral-500 dark:text-neutral-400">
            {selectedOptions[type] && (
              <span>
                {isOptionAvailable(type, selectedOptions[type]) 
                  ? "✓ Available" 
                  : "⚠ Currently unavailable"
                }
              </span>
            )}
          </div>
        </motion.div>
      ))}

      {/* Selected variant summary */}
      {selectedVariant && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-neutral-200 dark:border-neutral-700"
        >
          <div className="flex items-start gap-3">
            <Package className="h-5 w-5 text-[var(--brand-gold)] mt-0.5 flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <h4 className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                Selected Variant
              </h4>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                {selectedVariant.variant_name}
              </p>
              <div className="flex flex-wrap gap-1">
                {Object.entries(selectedVariant.variant_values).map(([type, value]) => (
                  <Badge key={`${type}-${value}`} variant="secondary" className="text-xs">
                    {type}: {value}
                  </Badge>
                ))}
              </div>
              {!selectedVariant.is_available && (
                <Badge variant="destructive" className="text-xs">
                  Currently Unavailable
                </Badge>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
